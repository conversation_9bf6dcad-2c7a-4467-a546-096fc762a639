import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { notifyAssignment } from './assignment-utils';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: updaterId } = ctx;
  const { id, receivedAt, assignUserId, assignmentChange } = input;

  await asyncTx(async (tx) => {
    // Get current inquiry for version creation
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id },
      include: { assignUsers: { include: { user: true } } },
    });

    // Data for updating the main inquiry (excluding fields that shouldn't be updated)
    const updateData: Prisma.InquiryUpdateInput = {
      title: input.title,
      description: input.description,
      address: input.address,
      memo: input.memo,
      receivedAt: input.receivedAt,
      fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
      receptionRoute: { connect: { id: input.receptionRouteId } },
      receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
      images: { deleteMany: {}, createMany: { data: input.images } },
      assignUsers: assignUserId
        ? { deleteMany: {}, create: { userId: assignUserId } }
        : { deleteMany: {} },
    };

    // Data for creating the version record
    const versionData: Prisma.InquiryCreateInput = {
      title: input.title,
      description: input.description,
      address: input.address,
      memo: input.memo,
      receivedAt: input.receivedAt,
      receptionRoute: { connect: { id: input.receptionRouteId } },
      receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
      images: { createMany: { data: input.images } },
      assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
    };

    // Update the main inquiry
    await tx.inquiry.update({ data: updateData, where: { id } });

    // Create version
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Handle assignment notifications
    if (assignmentChange) {
      await notifyAssignment(tx, assignmentChange, id, input.title, updaterId);
    }
  });

  return 'OK';
};

export const updateInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(updateInquiryMutation);
