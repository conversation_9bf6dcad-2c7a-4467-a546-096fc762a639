import { isNullish } from 'common';
import type { Tx } from '../../../../.prisma';
import type { Context } from '../../types';

type AssignmentChange = {
  type: 'assign' | 'unassign' | 'reassign' | 'no-change';
  currentAssigneeId?: string;
  newAssigneeId?: string;
  shouldNotifyCurrentAssignee: boolean;
  shouldNotifyNewAssignee: boolean;
};

type NotificationData = {
  inquiryId: string;
  inquiryTitle: string;
  assigneeId: string;
  assignerId: string;
  type: 'assign' | 'unassign' | 'reassign';
};

/**
 * Analyzes assignment changes and determines what notifications should be sent
 */
export const analyzeAssignmentChange = (
  currentAssigneeId: string | null | undefined,
  newAssigneeId: string | null | undefined,
  ctx: Context,
): AssignmentChange => {
  const hasCurrentAssignee = !isNullish(currentAssigneeId);
  const hasNewAssignee = !isNullish(newAssigneeId);
  const isSameAssignee = currentAssigneeId === newAssigneeId;
  const isSelfAssignment = currentAssigneeId === ctx.userId || newAssigneeId === ctx.userId;

  // 自分自身への割り当ての場合は通知を送信しない
  if (isSelfAssignment) {
    return {
      type: 'no-change',
      currentAssigneeId: currentAssigneeId || undefined,
      newAssigneeId: newAssigneeId || undefined,
      shouldNotifyCurrentAssignee: false,
      shouldNotifyNewAssignee: false,
    };
  }

  // 担当者が変更されていない場合は通知を送信しない
  if (isSameAssignee) {
    return {
      type: 'no-change',
      currentAssigneeId: currentAssigneeId || undefined,
      newAssigneeId: newAssigneeId || undefined,
      shouldNotifyCurrentAssignee: false,
      shouldNotifyNewAssignee: false,
    };
  }

  // 新規割り当ての場合は新規割り当てされたユーザーに通知を送信する(自分自身に通知を送信しない)
  if (!hasCurrentAssignee && hasNewAssignee) {
    return {
      type: 'assign',
      newAssigneeId,
      shouldNotifyCurrentAssignee: false,
      shouldNotifyNewAssignee: true,
    };
  }

  // 割り当て解除の場合は現在の担当者に通知を送信する
  if (hasCurrentAssignee && !hasNewAssignee) {
    return {
      type: 'unassign',
      currentAssigneeId,
      shouldNotifyCurrentAssignee: true,
      shouldNotifyNewAssignee: false,
    };
  }

  // 再割り当ての場合は現在の担当者と新規割り当てのユーザーに通知を送信する
  if (hasCurrentAssignee && hasNewAssignee) {
    return {
      type: 'reassign',
      currentAssigneeId,
      newAssigneeId,
      shouldNotifyCurrentAssignee: true,
      shouldNotifyNewAssignee: true,
    };
  }

  throw Error('Invalid assignment change detected');
};

/**
 * Creates a notification for inquiry assignment changes
 */
export const createInquiryAssignmentNotification = async (tx: Tx, data: NotificationData) => {
  const messageTemplates = {
    assign: {
      title: '問い合わせが割り当てられました',
      description: `問い合わせ「${data.inquiryTitle}」があなたに割り当てられました。`,
    },
    unassign: {
      title: '問い合わせの割り当てが解除されました',
      description: `問い合わせ「${data.inquiryTitle}」の割り当てが解除されました。`,
    },
    reassign: {
      title: '問い合わせの割り当てが変更されました',
      description: `問い合わせ「${data.inquiryTitle}」の割り当てが変更されました。`,
    },
  };

  const message = messageTemplates[data.type];

  const notification = await tx.notification.create({
    data: {
      title: message.title,
      description: message.description,
      type: 'inquiryAssignment',
      scope: 'user',
      inquiryId: data.inquiryId,
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.assigneeId,
    },
  });
};

/**
 * Handles all assignment notifications based on the change analysis
 */
export const notifyAssignment = async (
  tx: Tx,
  change: AssignmentChange,
  inquiryId: string,
  inquiryTitle: string,
  actorId: string,
) => {
  const notifications: Promise<void>[] = [];

  if (change.shouldNotifyCurrentAssignee && change.currentAssigneeId) {
    const notificationType = change.type === 'unassign' ? 'unassign' : 'reassign';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.currentAssigneeId,
        assignerId: actorId,
        type: notificationType,
      }),
    );
  }

  if (change.shouldNotifyNewAssignee && change.newAssigneeId) {
    const notificationType = change.type === 'assign' ? 'assign' : 'assign';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.newAssigneeId,
        assignerId: actorId,
        type: notificationType,
      }),
    );
  }

  await Promise.all(notifications);
};
